package main

import (
	"os"
	"strings"
	"testing"
)

func TestGenerateID(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"label_enter_detail_v2_cc_full", "Label_enter_detail_v2_cc_full"},
		{"api_error_invalid_request", "Api_Error_invalid_request"},
		{"error_network_timeout", "Error_network_timeout"},
		{"button_submit_form", "Button_submit_form"},
		{"", ""},
	}

	for _, test := range tests {
		result := GenerateID(test.input)
		if result != test.expected {
			t.<PERSON>rrorf("GenerateID(%s) = %s; expected %s", test.input, result, test.expected)
		}
	}
}

func TestExtractModuleInfo(t *testing.T) {
	tests := []struct {
		input              string
		expectedModuleKey  string
		expectedModuleName string
	}{
		{"label_enter_detail_v2_cc_full", "label", "Label"},
		{"api_error_invalid_request", "api_error", "Api_Error"},
		{"error_network_timeout", "error", "Error"},
		{"button_submit_form", "button", "Button"},
	}

	for _, test := range tests {
		result := ExtractModuleInfo(test.input)
		if result.ModuleKey != test.expectedModuleKey {
			t.Errorf("ExtractModuleInfo(%s).ModuleKey = %s; expected %s",
				test.input, result.ModuleKey, test.expectedModuleKey)
		}
		if result.ModuleName != test.expectedModuleName {
			t.Errorf("ExtractModuleInfo(%s).ModuleName = %s; expected %s",
				test.input, result.ModuleName, test.expectedModuleName)
		}
	}
}

func TestValidatePhraseKeyFormat(t *testing.T) {
	validTests := []string{
		"label_test",
		"api_error_test",
		"error_test",
		"button_test",
	}

	for _, test := range validTests {
		err := ValidatePhraseKeyFormat(test, 1)
		if err != nil {
			t.Errorf("ValidatePhraseKeyFormat(%s) should be valid, got error: %v", test, err)
		}
	}

	invalidTests := []string{
		"invalid_test",
		"unknown_module",
		"test_label",
	}

	for _, test := range invalidTests {
		err := ValidatePhraseKeyFormat(test, 1)
		if err == nil {
			t.Errorf("ValidatePhraseKeyFormat(%s) should be invalid, but got no error", test)
		}
	}
}

func TestParseInputText(t *testing.T) {
	validInput := `label_test|Test English|ทดสอบไทย
api_error_invalid|Invalid|ไม่ถูกต้อง`

	phrases, err := ParseInputText(validInput)
	if err != nil {
		t.Errorf("ParseInputText should succeed, got error: %v", err)
	}

	if len(phrases) != 2 {
		t.Errorf("Expected 2 phrases, got %d", len(phrases))
	}

	if phrases[0].PhraseKey != "label_test" {
		t.Errorf("Expected first phrase key 'label_test', got '%s'", phrases[0].PhraseKey)
	}

	if phrases[1].PhraseKey != "api_error_invalid" {
		t.Errorf("Expected second phrase key 'api_error_invalid', got '%s'", phrases[1].PhraseKey)
	}
}

func TestParseInputTextInvalid(t *testing.T) {
	invalidInputs := []string{
		"invalid_format",
		"too|many|fields|here",
		"empty||field",
		"invalid_module|text|ข้อความ",
	}

	for _, input := range invalidInputs {
		_, err := ParseInputText(input)
		if err == nil {
			t.Errorf("ParseInputText should fail for invalid input: %s", input)
		}
	}
}

func TestValidateConfig(t *testing.T) {
	validConfig := DefaultConfig()
	err := ValidateConfig(validConfig)
	if err != nil {
		t.Errorf("Default config should be valid, got error: %v", err)
	}

	invalidConfigs := []Config{
		{ChangesetID: "", Author: "test", Comment: "test", InputFile: "test", OutputFile: "test"},
		{ChangesetID: "test", Author: "", Comment: "test", InputFile: "test", OutputFile: "test"},
		{ChangesetID: "test", Author: "test", Comment: "", InputFile: "test", OutputFile: "test"},
		{ChangesetID: "test", Author: "test", Comment: "test", InputFile: "", OutputFile: "test"},
		{ChangesetID: "test", Author: "test", Comment: "test", InputFile: "test", OutputFile: ""},
	}

	for _, config := range invalidConfigs {
		err := ValidateConfig(config)
		if err == nil {
			t.Errorf("Config should be invalid: %+v", config)
		}
	}
}

func TestTransformToChangeset(t *testing.T) {
	phrases := []PhraseInput{
		{PhraseKey: "label_test", EnglishText: "Test", ThaiText: "ทดสอบ"},
	}
	config := DefaultConfig()

	changeset := TransformToChangeset(phrases, config)

	if len(changeset.DatabaseChangeLog) != 1 {
		t.Errorf("Expected 1 changeset, got %d", len(changeset.DatabaseChangeLog))
	}

	cs := changeset.DatabaseChangeLog[0]["changeSet"]
	if cs.ID != config.ChangesetID {
		t.Errorf("Expected changeset ID '%s', got '%s'", config.ChangesetID, cs.ID)
	}

	if cs.Author != config.Author {
		t.Errorf("Expected author '%s', got '%s'", config.Author, cs.Author)
	}
}

func TestGenerateJSONString(t *testing.T) {
	phrases := []PhraseInput{
		{PhraseKey: "label_test", EnglishText: "Test", ThaiText: "ทดสอบ"},
	}
	config := DefaultConfig()
	changeset := TransformToChangeset(phrases, config)

	jsonStr, err := GenerateJSONString(changeset)
	if err != nil {
		t.Errorf("GenerateJSONString should succeed, got error: %v", err)
	}

	if !strings.Contains(jsonStr, "databaseChangeLog") {
		t.Errorf("JSON should contain 'databaseChangeLog'")
	}

	if !strings.Contains(jsonStr, "Label_test") {
		t.Errorf("JSON should contain generated ID 'Label_test'")
	}
}

func TestFileOperations(t *testing.T) {
	// Create a temporary input file
	tempInput := "test_input.txt"
	tempOutput := "test_output.json"

	defer func() {
		os.Remove(tempInput)
		os.Remove(tempOutput)
	}()

	// Write test data
	testData := "label_test|Test English|ทดสอบไทย"
	err := os.WriteFile(tempInput, []byte(testData), 0644)
	if err != nil {
		t.Fatalf("Failed to create test input file: %v", err)
	}

	// Parse the file
	phrases, err := ParseInputFile(tempInput)
	if err != nil {
		t.Errorf("ParseInputFile should succeed, got error: %v", err)
	}

	if len(phrases) != 1 {
		t.Errorf("Expected 1 phrase, got %d", len(phrases))
	}

	// Transform and write output
	config := DefaultConfig()
	changeset := TransformToChangeset(phrases, config)

	err = WriteJSONToFile(changeset, tempOutput)
	if err != nil {
		t.Errorf("WriteJSONToFile should succeed, got error: %v", err)
	}

	// Verify output file exists
	if _, err := os.Stat(tempOutput); os.IsNotExist(err) {
		t.Errorf("Output file should exist")
	}
}
