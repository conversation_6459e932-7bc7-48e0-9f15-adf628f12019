package main

import (
	"fmt"
	"strings"
	"unicode/utf8"
)

// ValidationResult holds validation results
type ValidationResult struct {
	IsValid bool
	Errors  []string
}

// ValidatePhraseInputs validates a slice of phrase inputs
func ValidatePhraseInputs(phrases []PhraseInput) ValidationResult {
	var errors []string

	if len(phrases) == 0 {
		errors = append(errors, "no phrases provided for validation")
		return ValidationResult{IsValid: false, Errors: errors}
	}

	for i, phrase := range phrases {
		if err := ValidateSinglePhrase(phrase, i+1); err != nil {
			errors = append(errors, err.Error())
		}
	}

	return ValidationResult{
		IsValid: len(errors) == 0,
		Errors:  errors,
	}
}

// ValidateSinglePhrase validates a single phrase input
func ValidateSinglePhrase(phrase PhraseInput, index int) error {
	// Check for empty fields
	if phrase.PhraseKey == "" {
		return fmt.Errorf("phrase %d: phrase_key cannot be empty", index)
	}
	if phrase.EnglishText == "" {
		return fmt.Errorf("phrase %d: english_text cannot be empty", index)
	}
	if phrase.ThaiText == "" {
		return fmt.Errorf("phrase %d: thai_text cannot be empty", index)
	}

	// Validate UTF-8 encoding
	if !utf8.ValidString(phrase.PhraseKey) {
		return fmt.Errorf("phrase %d: phrase_key contains invalid UTF-8 characters", index)
	}
	if !utf8.ValidString(phrase.EnglishText) {
		return fmt.Errorf("phrase %d: english_text contains invalid UTF-8 characters", index)
	}
	if !utf8.ValidString(phrase.ThaiText) {
		return fmt.Errorf("phrase %d: thai_text contains invalid UTF-8 characters", index)
	}

	// Validate phrase key format
	if err := ValidatePhraseKeyFormat(phrase.PhraseKey, index); err != nil {
		return err
	}

	return nil
}

// ValidatePhraseKeyFormat validates the phrase key format
func ValidatePhraseKeyFormat(phraseKey string, index int) error {
	supportedPrefixes := []string{"label_", "api_error_", "error_", "button_"}

	for _, prefix := range supportedPrefixes {
		if strings.HasPrefix(phraseKey, prefix) {
			return nil
		}
	}

	return fmt.Errorf("phrase %d: unsupported module type in phrase_key '%s'. Supported prefixes: %v",
		index, phraseKey, supportedPrefixes)
}

// ValidateLineFormat validates a single line format
func ValidateLineFormat(line string, lineNumber int) error {
	if strings.TrimSpace(line) == "" {
		return nil // Empty lines are allowed
	}

	parts := strings.Split(line, "|")
	if len(parts) != 3 {
		return fmt.Errorf("line %d: invalid format, expected 3 fields separated by '|', got %d fields", lineNumber, len(parts))
	}

	// Check for empty fields after trimming
	for i, part := range parts {
		if strings.TrimSpace(part) == "" {
			fieldNames := []string{"phrase_key", "english_text", "thai_text"}
			return fmt.Errorf("line %d: %s cannot be empty", lineNumber, fieldNames[i])
		}
	}

	return nil
}

// GetSupportedModuleTypes returns the list of supported module types
func GetSupportedModuleTypes() []string {
	return []string{"label", "api_error", "error", "button"}
}
