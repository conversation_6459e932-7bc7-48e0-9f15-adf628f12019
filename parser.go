package main

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"unicode/utf8"
)

// ParseInputFile reads and parses the input file
func ParseInputFile(filename string) ([]PhraseInput, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open input file %s: %w", filename, err)
	}
	defer file.Close()

	var phrases []PhraseInput
	scanner := bufio.NewScanner(file)
	lineNumber := 0

	for scanner.Scan() {
		lineNumber++
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines
		if line == "" {
			continue
		}

		phrase, err := parseLine(line, lineNumber)
		if err != nil {
			return nil, err
		}

		phrases = append(phrases, phrase)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	if len(phrases) == 0 {
		return nil, fmt.<PERSON><PERSON><PERSON>("no valid phrases found in input file")
	}

	return phrases, nil
}

// parseLine parses a single line of pipe-delimited data
func parseLine(line string, lineNumber int) (PhraseInput, error) {
	parts := strings.Split(line, "|")

	if len(parts) != 3 {
		return PhraseInput{}, fmt.Errorf("line %d: invalid format, expected 3 fields separated by '|', got %d fields", lineNumber, len(parts))
	}

	phraseKey := strings.TrimSpace(parts[0])
	englishText := strings.TrimSpace(parts[1])
	thaiText := strings.TrimSpace(parts[2])

	// Validate that none of the fields are empty
	if phraseKey == "" {
		return PhraseInput{}, fmt.Errorf("line %d: phrase_key cannot be empty", lineNumber)
	}
	if englishText == "" {
		return PhraseInput{}, fmt.Errorf("line %d: english_text cannot be empty", lineNumber)
	}
	if thaiText == "" {
		return PhraseInput{}, fmt.Errorf("line %d: thai_text cannot be empty", lineNumber)
	}

	// Validate UTF-8 encoding
	if !utf8.ValidString(phraseKey) {
		return PhraseInput{}, fmt.Errorf("line %d: phrase_key contains invalid UTF-8 characters", lineNumber)
	}
	if !utf8.ValidString(englishText) {
		return PhraseInput{}, fmt.Errorf("line %d: english_text contains invalid UTF-8 characters", lineNumber)
	}
	if !utf8.ValidString(thaiText) {
		return PhraseInput{}, fmt.Errorf("line %d: thai_text contains invalid UTF-8 characters", lineNumber)
	}

	// Validate phrase_key format for supported module types
	if err := validatePhraseKey(phraseKey, lineNumber); err != nil {
		return PhraseInput{}, err
	}

	return PhraseInput{
		PhraseKey:   phraseKey,
		EnglishText: englishText,
		ThaiText:    thaiText,
	}, nil
}

// validatePhraseKey validates that the phrase key starts with a supported module type
func validatePhraseKey(phraseKey string, lineNumber int) error {
	supportedPrefixes := []string{"label_", "api_error_", "error_", "button_"}

	for _, prefix := range supportedPrefixes {
		if strings.HasPrefix(phraseKey, prefix) {
			return nil
		}
	}

	return fmt.Errorf("line %d: unsupported module type in phrase_key '%s'. Supported prefixes: %v",
		lineNumber, phraseKey, supportedPrefixes)
}

// ParseInputText parses input from a text string (alternative to file input)
func ParseInputText(text string) ([]PhraseInput, error) {
	var phrases []PhraseInput
	lines := strings.Split(text, "\n")
	lineNumber := 0

	for _, line := range lines {
		lineNumber++
		line = strings.TrimSpace(line)

		// Skip empty lines
		if line == "" {
			continue
		}

		phrase, err := parseLine(line, lineNumber)
		if err != nil {
			return nil, err
		}

		phrases = append(phrases, phrase)
	}

	if len(phrases) == 0 {
		return nil, fmt.Errorf("no valid phrases found in input text")
	}

	return phrases, nil
}
