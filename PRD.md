# Product Requirements Document (PRD)
## Phrase Data to Liquibase Script Converter

### 1. Overview
This application converts phrase data from pipe-delimited format to Liquibase MongoDB changeset scripts for upserting phrase configurations.

### 2. Purpose
To automate the generation of Liquibase changeset files for phrase data migration, ensuring consistent format and reducing manual errors in database migration scripts.

### 3. Input Format
**Input File:** `input.txt`
**Format:** Pipe-delimited text format (supports multiple lines)
```
phrase_key|english_text|thai_text
```

**Single Line Example:**
```
label_enter_detail_v2_cc_full|Full amount|เต็มจำนวน
```

**Multiple Lines Example:**
```
label_enter_detail_v2_cc_full|Full amount|เต็มจำนวน
api_error_invalid_request|Invalid request|คำขอไม่ถูกต้อง
error_network_timeout|Network timeout|เครือข่ายหมดเวลา
button_submit_form|Submit|ส่ง
```

### 4. Output Format
**Output File:** `db.changeset.json`
**Format:** Liquibase MongoDB changeset JSON format following the existing pattern
**Note:** For multiple input lines, all phrases will be included in a single changeset with multiple update operations:

```json
{
  "databaseChangeLog": [
    {
      "changeSet": {
        "id": "rocket-r14-02-phrase-common-payment-credit_v3",
        "author": "rocket",
        "comment": "[r14] Upsert Phrases For Common Payment and Credit Card",
        "changes": [
          {
            "runCommand": {
              "command": {
                "$rawJson": {
                  "update": "phrases_config_migration",
                  "updates": [
                    {
                      "q": {
                        "_id": "Label_enter_detail_v2_cc_full"
                      },
                      "u": {
                        "$set": {
                          "_id": "Label_enter_detail_v2_cc_full",
                          "phrase_key": "enter_detail_v2_cc_full",
                          "module_key": "label",
                          "module_name": "Label",
                          "status": "Published",
                          "temp_status": "Published",
                          "last_updated_time": {
                            "$date": "2025-07-31T04:05:18.802827Z"
                          },
                          "created_time": {
                            "$date": "2025-07-31T04:05:18.802827Z"
                          },
                          "updated_by": "rocket",
                          "en": "Full amount",
                          "th": "เต็มจำนวน",
                          "_class": "com.tmb.commonservice.masterdata.phrases.model.Phrase"
                        }
                      },
                      "upsert": true,
                      "multi": false
                    }
                  ],
                  "ordered": true
                }
              }
            }
          }
        ]
      }
    }
  ]
}
```

### 5. Functional Requirements

#### 5.1 Core Features
- **Input Processing**: Parse multiple lines of pipe-delimited phrase data
- **Data Transformation**: Convert to Liquibase MongoDB changeset format
- **ID Generation**: Generate proper MongoDB document IDs and phrase keys
- **Module Extraction**: Extract module_key and module_name from phrase_key
- **Timestamp Generation**: Generate current timestamp for created_time and last_updated_time
- **Batch Processing**: Handle multiple phrases in a single changeset
- **JSON Output**: Generate properly formatted JSON changeset

#### 5.2 Data Mapping Rules
| Input Field | Output Field | Transformation Rule |
|-------------|--------------|-------------------|
| phrase_key | phrase_key | Direct mapping |
| phrase_key | _id | Convert using ID formatting rules (see 5.2.1) |
| phrase_key | module_key | Extract module from phrase_key (see 5.2.2) |
| phrase_key | module_name | Extract and format module name (see 5.2.2) |
| english_text | en | Direct mapping |
| thai_text | th | Direct mapping |
| - | status | Always "Published" |
| - | temp_status | Always "Published" |
| - | created_time | Current timestamp in MongoDB date format |
| - | last_updated_time | Current timestamp in MongoDB date format |
| - | updated_by | Always "rocket" |
| - | _class | Always "com.tmb.commonservice.masterdata.phrases.model.Phrase" |

#### 5.2.1 ID Formatting Rules
The `_id` field generation follows specific capitalization rules:

**Standard Rule:**
- Capitalize the first character of the phrase_key
- Example: `label_enter_detail_v2_cc_full` → `Label_enter_detail_v2_cc_full`

**Special Case for API Error Phrases:**
- If phrase_key starts with `api_error_`, apply special formatting:
  - Capitalize first character: `api` → `Api`
  - Capitalize character after first underscore: `error` → `Error`
  - Keep the rest unchanged
- Example: `api_error_phrase_key` → `Api_Error_phrase_key`

**Implementation Logic:**
```
if (phrase_key.startsWith("api_error_")) {
    _id = "Api_Error_" + phrase_key.substring(10); // Remove "api_error_" and add "Api_Error_"
} else {
    _id = phrase_key.charAt(0).toUpperCase() + phrase_key.substring(1);
}
```

#### 5.2.2 Module Key and Module Name Extraction Rules
The `module_key` and `module_name` fields are dynamically extracted from the phrase_key prefix:

**Supported Module Types:**
- `label` → module_key: "label", module_name: "Label"
- `api_error` → module_key: "api_error", module_name: "Api_Error"
- `error` → module_key: "error", module_name: "Error"
- `button` → module_key: "button", module_name: "Button"

**Extraction Rules:**
1. **Standard modules** (label, error, button):
   - module_key: Extract prefix before first underscore (lowercase)
   - module_name: Capitalize first letter of module_key

2. **Special case for api_error**:
   - module_key: "api_error" (lowercase)
   - module_name: "Api_Error" (capitalize both parts)

**Implementation Logic:**
```
if (phrase_key.startsWith("api_error_")) {
    module_key = "api_error";
    module_name = "Api_Error";
} else {
    // Extract prefix before first underscore
    String prefix = phrase_key.split("_")[0];
    module_key = prefix.toLowerCase();
    module_name = prefix.charAt(0).toUpperCase() + prefix.substring(1).toLowerCase();
}
```

**Examples:**
- `label_enter_detail_v2_cc_full` → module_key: "label", module_name: "Label"
- `api_error_invalid_request` → module_key: "api_error", module_name: "Api_Error"
- `error_network_timeout` → module_key: "error", module_name: "Error"
- `button_submit_form` → module_key: "button", module_name: "Button"

#### 5.2.3 Timestamp Generation Rules
Both `created_time` and `last_updated_time` fields must use the current timestamp when the script is generated.

**Format Requirements:**
- Use MongoDB date format: `{"$date": "ISO8601_timestamp"}`
- ISO8601 format with milliseconds and UTC timezone
- Example: `{"$date": "2025-08-02T10:30:45.123Z"}`

**Implementation Logic:**
```
// Generate current timestamp in ISO8601 format
String currentTimestamp = new Date().toISOString();
// Format for MongoDB
{
  "created_time": {"$date": currentTimestamp},
  "last_updated_time": {"$date": currentTimestamp}
}
```

#### 5.3 Changeset Metadata
- **id**: Configurable pattern (default: "rocket-r14-02-phrase-common-payment-credit_v3")
- **author**: Configurable (default: "rocket")
- **comment**: Configurable (default: "[r14] Upsert Phrases For Common Payment and Credit Card")

### 6. Technical Requirements

#### 6.1 Input Validation
- Validate pipe-delimited format (exactly 3 fields per line)
- Process multiple lines from input file
- Check for empty fields and empty lines
- Validate UTF-8 encoding for Thai text
- Handle special characters in JSON output
- Validate phrase_key format for proper ID generation
- Validate phrase_key prefix for supported module types (label, api_error, error, button)
- Skip empty lines and handle line endings properly

#### 6.2 Output Requirements
- Generate valid JSON format
- Proper MongoDB date format: `{"$date": "ISO8601_timestamp"}` with current timestamp
- Ensure proper escaping of special characters
- Maintain consistent indentation and formatting
- Consistent timestamp format across all generated records
- Single changeset containing multiple update operations for multiple input lines
- Proper array structure for multiple updates in MongoDB format

#### 6.3 Error Handling
- Invalid input format errors
- Missing required fields
- JSON generation errors
- File I/O errors (reading `input.txt`, writing `db.changeset.json`)
- File not found errors
- Unsupported module type errors (phrase_key with unknown prefix)

### 7. User Interface Requirements

#### 7.1 Input Methods
- **File Input**: Read from `input.txt` file with multiple lines of pipe-delimited data (default)
- **Text Input**: Direct text input area supporting multiple lines (alternative method)
- **Batch Processing**: Process all lines in a single operation

#### 7.2 Configuration Options
- Changeset ID pattern
- Author name
- Comment text
- Input file path (default: `input.txt`)
- Output file path (default: `db.changeset.json`)

#### 7.3 Output Options
- **File Output**: Generate `db.changeset.json` file
- **Copy to Clipboard**: Copy generated JSON to clipboard
- **Preview**: Display formatted JSON before saving

### 8. Non-Functional Requirements

#### 8.1 Performance
- Process up to 1000 phrase entries efficiently in a single batch
- Generate output within 5 seconds for typical datasets
- Handle large input files with multiple lines efficiently

#### 8.2 Usability
- Simple, intuitive interface
- Clear error messages
- Preview functionality before final generation

#### 8.3 Compatibility
- Support modern web browsers
- Handle Thai Unicode characters properly
- Generate MongoDB-compatible JSON

### 9. Success Criteria
- Successfully convert pipe-delimited phrase data to valid Liquibase changeset
- Generated JSON passes MongoDB syntax validation
- Proper handling of Thai Unicode characters
- Correct ID formatting for both standard and API error phrases
- Correct module_key and module_name extraction for all supported module types
- User can easily configure changeset metadata
- Error handling provides clear feedback to users

### 10. Future Enhancements
- Support for additional languages beyond English and Thai
- Bulk processing of multiple changeset files
- Integration with version control systems
- Template management for different changeset patterns
- Validation against existing phrase database
