# Phrase Data to Liquibase Script Converter

A Go application that converts phrase data from pipe-delimited format to Liquibase MongoDB changeset scripts for upserting phrase configurations.

## Features

- ✅ Parse pipe-delimited phrase data from files
- ✅ Generate MongoDB-compatible Liquibase changeset JSON
- ✅ Support for multiple module types (label, api_error, error, button)
- ✅ Automatic ID generation with special handling for API error phrases
- ✅ Module key and name extraction
- ✅ Configurable changeset metadata
- ✅ Comprehensive input validation
- ✅ UTF-8 encoding support for Thai text
- ✅ Batch processing of multiple phrases

## Installation

### Prerequisites
- Go 1.19 or later

### Build from Source
```bash
git clone <repository-url>
cd phrase-generator-go
go build -o phrase-generator
```

## Usage

### Basic Usage
```bash
./phrase-generator
```

This will read from `input.txt` and generate `db.changeset.json`.

### Command Line Options
```bash
./phrase-generator [options]

Options:
  -author string
        Author name (default "rocket")
  -comment string
        Changeset comment (default "[r14] Upsert Phrases For Common Payment and Credit Card")
  -id string
        Changeset ID (default "rocket-r14-02-phrase-common-payment-credit_v3")
  -input string
        Input file path (default "input.txt")
  -output string
        Output file path (default "db.changeset.json")
```

### Examples

#### Custom Configuration
```bash
./phrase-generator \
  -input my_phrases.txt \
  -output my_changeset.json \
  -id "custom-changeset-v1" \
  -author "developer" \
  -comment "Custom phrase updates"
```

#### Help
```bash
./phrase-generator -h
```

## Input Format

The input file should contain pipe-delimited data with exactly 3 fields per line:

```
phrase_key|english_text|thai_text
```

### Example Input File
```
label_enter_detail_v2_cc_full|Full amount|เต็มจำนวน
api_error_invalid_request|Invalid request|คำขอไม่ถูกต้อง
error_network_timeout|Network timeout|เครือข่ายหมดเวลา
button_submit_form|Submit|ส่ง
```

### Supported Module Types

The application supports the following module prefixes:

- `label_*` → Module: "label", Name: "Label"
- `api_error_*` → Module: "api_error", Name: "Api_Error"
- `error_*` → Module: "error", Name: "Error"
- `button_*` → Module: "button", Name: "Button"

## Output Format

The application generates a Liquibase MongoDB changeset in JSON format:

```json
{
  "databaseChangeLog": [
    {
      "changeSet": {
        "id": "rocket-r14-02-phrase-common-payment-credit_v3",
        "author": "rocket",
        "comment": "[r14] Upsert Phrases For Common Payment and Credit Card",
        "changes": [
          {
            "runCommand": {
              "command": {
                "$rawJson": {
                  "update": "phrases_config_migration",
                  "updates": [
                    {
                      "q": { "_id": "Label_enter_detail_v2_cc_full" },
                      "u": {
                        "$set": {
                          "_id": "Label_enter_detail_v2_cc_full",
                          "phrase_key": "label_enter_detail_v2_cc_full",
                          "module_key": "label",
                          "module_name": "Label",
                          "status": "Published",
                          "temp_status": "Published",
                          "last_updated_time": {"$date": "2025-08-02T05:10:20.073Z"},
                          "created_time": {"$date": "2025-08-02T05:10:20.073Z"},
                          "updated_by": "rocket",
                          "en": "Full amount",
                          "th": "เต็มจำนวน",
                          "_class": "com.tmb.commonservice.masterdata.phrases.model.Phrase"
                        }
                      },
                      "upsert": true,
                      "multi": false
                    }
                  ],
                  "ordered": true
                }
              }
            }
          }
        ]
      }
    }
  ]
}
```

## ID Generation Rules

### Standard Rule
- Capitalize the first character of the phrase_key
- Example: `label_enter_detail_v2_cc_full` → `Label_enter_detail_v2_cc_full`

### Special Case for API Error Phrases
- If phrase_key starts with `api_error_`, apply special formatting:
  - Replace `api_error_` with `Api_Error_`
- Example: `api_error_invalid_request` → `Api_Error_invalid_request`

## Validation

The application performs comprehensive validation:

- ✅ Pipe-delimited format (exactly 3 fields per line)
- ✅ Non-empty fields
- ✅ UTF-8 encoding validation
- ✅ Supported module type validation
- ✅ File existence checks

## Error Handling

The application provides clear error messages for:

- Invalid input format
- Missing required fields
- Unsupported module types
- File I/O errors
- JSON generation errors

## Testing

Run the test suite:

```bash
go test -v
```

## Examples Directory

The `examples/` directory contains sample input files:

- `single_phrase.txt` - Single phrase example
- `multiple_phrases.txt` - Multiple phrases example
- `invalid_input.txt` - Invalid module type example
- `malformed_input.txt` - Malformed format example

## License

[Add your license information here]

## Contributing

[Add contributing guidelines here]
