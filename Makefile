# Phrase Generator Makefile

.PHONY: build test clean run help

# Default target
all: build

# Build the application
build:
	go build -o phrase-generator

# Run tests
test:
	go test -v

# Clean build artifacts
clean:
	rm -f phrase-generator
	rm -f db.changeset.json
	rm -f examples/*.json

# Run with default input
run: build
	./phrase-generator

# Run with examples
run-single: build
	./phrase-generator -input examples/single_phrase.txt -output examples/single_output.json

run-multiple: build
	./phrase-generator -input examples/multiple_phrases.txt -output examples/multiple_output.json

# Show help
help: build
	./phrase-generator -h

# Install dependencies (if any)
deps:
	go mod tidy

# Format code
fmt:
	go fmt ./...

# Vet code
vet:
	go vet ./...

# Run all checks
check: fmt vet test

# Show usage examples
examples:
	@echo "Usage examples:"
	@echo "  make build          - Build the application"
	@echo "  make test           - Run tests"
	@echo "  make run            - Run with default input.txt"
	@echo "  make run-single     - Run with single phrase example"
	@echo "  make run-multiple   - Run with multiple phrases example"
	@echo "  make clean          - Clean build artifacts"
	@echo "  make help           - Show application help"
