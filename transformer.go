package main

// TransformToChangeset converts phrase inputs to Liquibase changeset format
func TransformToChangeset(phrases []PhraseInput, config Config) DatabaseChangeLog {
	// Generate current timestamp for all phrases
	currentTime := GenerateTimestamp()

	// Create update operations for all phrases
	var updates []UpdateOperation

	for _, phrase := range phrases {
		// Generate ID and extract module info
		id := GenerateID(phrase.PhraseKey)
		moduleInfo := ExtractModuleInfo(phrase.PhraseKey)

		// Create update operation
		updateOp := UpdateOperation{
			Q: map[string]interface{}{
				"_id": id,
			},
			U: map[string]interface{}{
				"$set": map[string]interface{}{
					"_id":               id,
					"phrase_key":        phrase.PhraseKey,
					"module_key":        moduleInfo.ModuleKey,
					"module_name":       moduleInfo.ModuleName,
					"status":            "Published",
					"temp_status":       "Published",
					"last_updated_time": currentTime,
					"created_time":      currentTime,
					"updated_by":        "rocket",
					"en":                phrase.EnglishText,
					"th":                phrase.ThaiText,
					"_class":            "com.tmb.commonservice.masterdata.phrases.model.Phrase",
				},
			},
			Upsert: true,
			Multi:  false,
		}

		updates = append(updates, updateOp)
	}

	// Create the raw JSON command
	rawJSON := RawJSON{
		Update:  "phrases_config_migration",
		Updates: updates,
		Ordered: true,
	}

	// Create the run command
	runCommand := RunCommand{
		Command: map[string]interface{}{
			"$rawJson": rawJSON,
		},
	}

	// Create the change
	change := Change{
		RunCommand: runCommand,
	}

	// Create the changeset
	changeset := ChangeSet{
		ID:      config.ChangesetID,
		Author:  config.Author,
		Comment: config.Comment,
		Changes: []Change{change},
	}

	// Create the database change log
	changeLog := DatabaseChangeLog{
		DatabaseChangeLog: []map[string]ChangeSet{
			{"changeSet": changeset},
		},
	}

	return changeLog
}
