package main

import (
	"flag"
	"fmt"
)

// ParseFlags parses command line flags and returns a Config
func ParseFlags() Config {
	config := DefaultConfig()

	flag.StringVar(&config.ChangesetID, "id", config.ChangesetID, "Changeset ID")
	flag.StringVar(&config.Author, "author", config.Author, "Author name")
	flag.StringVar(&config.Comment, "comment", config.Comment, "Changeset comment")
	flag.StringVar(&config.InputFile, "input", config.InputFile, "Input file path")
	flag.StringVar(&config.OutputFile, "output", config.OutputFile, "Output file path")

	flag.Parse()

	return config
}

// ValidateConfig validates the configuration
func ValidateConfig(config Config) error {
	if config.ChangesetID == "" {
		return fmt.Errorf("changeset ID cannot be empty")
	}
	if config.Author == "" {
		return fmt.Errorf("author cannot be empty")
	}
	if config.Comment == "" {
		return fmt.Errorf("comment cannot be empty")
	}
	if config.InputFile == "" {
		return fmt.Errorf("input file path cannot be empty")
	}
	if config.OutputFile == "" {
		return fmt.Errorf("output file path cannot be empty")
	}

	return nil
}

// PrintConfig prints the current configuration
func PrintConfig(config Config) {
	fmt.Printf("Configuration:\n")
	fmt.Printf("  Changeset ID: %s\n", config.ChangesetID)
	fmt.Printf("  Author: %s\n", config.Author)
	fmt.Printf("  Comment: %s\n", config.Comment)
	fmt.Printf("  Input File: %s\n", config.InputFile)
	fmt.Printf("  Output File: %s\n", config.OutputFile)
	fmt.Println()
}
