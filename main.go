package main

import (
	"flag"
	"fmt"
	"os"
)

func main() {
	// Show help if requested
	if len(os.Args) > 1 && (os.Args[1] == "-h" || os.Args[1] == "--help") {
		showHelp()
		return
	}

	// Parse command line flags
	config := ParseFlags()

	// Validate configuration
	if err := ValidateConfig(config); err != nil {
		HandleError(NewConfigError(err.Error()))
	}

	// Print configuration
	PrintConfig(config)

	// Check if input file exists
	if _, err := os.Stat(config.InputFile); os.IsNotExist(err) {
		HandleError(NewFileIOError(fmt.Sprintf("input file '%s' does not exist", config.InputFile), err))
	}

	// Parse input file
	fmt.Printf("Parsing input file: %s\n", config.InputFile)
	phrases, err := ParseInputFile(config.InputFile)
	if err != nil {
		HandleError(err)
	}

	fmt.Printf("Successfully parsed %d phrases\n", len(phrases))

	// Transform to changeset
	fmt.Println("Transforming to Liquibase changeset format...")
	changeset := TransformToChangeset(phrases, config)

	// Generate JSON output
	fmt.Printf("Writing output to: %s\n", config.OutputFile)
	if err := WriteJSONToFile(changeset, config.OutputFile); err != nil {
		HandleError(NewJSONError("failed to write output file", err))
	}

	fmt.Printf("✅ Successfully generated changeset file: %s\n", config.OutputFile)
	fmt.Printf("📊 Processed %d phrases\n", len(phrases))
}

func showHelp() {
	fmt.Println("Phrase Data to Liquibase Script Converter")
	fmt.Println("=========================================")
	fmt.Println()
	fmt.Println("This application converts phrase data from pipe-delimited format to Liquibase MongoDB changeset scripts.")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Printf("  %s [options]\n", os.Args[0])
	fmt.Println()
	fmt.Println("Options:")
	flag.PrintDefaults()
	fmt.Println()
	fmt.Println("Input Format:")
	fmt.Println("  phrase_key|english_text|thai_text")
	fmt.Println()
	fmt.Println("Example:")
	fmt.Println("  label_enter_detail_v2_cc_full|Full amount|เต็มจำนวน")
	fmt.Println("  api_error_invalid_request|Invalid request|คำขอไม่ถูกต้อง")
	fmt.Println()
	fmt.Println("Supported Module Types:")
	fmt.Println("  - label_*")
	fmt.Println("  - api_error_*")
	fmt.Println("  - error_*")
	fmt.Println("  - button_*")
	fmt.Println()
}
