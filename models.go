package main

import "time"

// PhraseInput represents a single line of input data
type PhraseInput struct {
	PhraseKey   string
	EnglishText string
	ThaiText    string
}

// Config holds configuration options for the application
type Config struct {
	ChangesetID string
	Author      string
	Comment     string
	InputFile   string
	OutputFile  string
}

// MongoDate represents MongoDB date format
type MongoDate struct {
	Date string `json:"$date"`
}

// PhraseDocument represents the MongoDB document structure
type PhraseDocument struct {
	ID              string    `json:"_id"`
	PhraseKey       string    `json:"phrase_key"`
	ModuleKey       string    `json:"module_key"`
	ModuleName      string    `json:"module_name"`
	Status          string    `json:"status"`
	TempStatus      string    `json:"temp_status"`
	LastUpdatedTime MongoDate `json:"last_updated_time"`
	CreatedTime     MongoDate `json:"created_time"`
	UpdatedBy       string    `json:"updated_by"`
	En              string    `json:"en"`
	Th              string    `json:"th"`
	Class           string    `json:"_class"`
}

// UpdateOperation represents a single MongoDB update operation
type UpdateOperation struct {
	Q      map[string]interface{} `json:"q"`
	U      map[string]interface{} `json:"u"`
	Upsert bool                   `json:"upsert"`
	Multi  bool                   `json:"multi"`
}

// RawJSON represents the MongoDB command structure
type RawJSON struct {
	Update  string            `json:"update"`
	Updates []UpdateOperation `json:"updates"`
	Ordered bool              `json:"ordered"`
}

// RunCommand represents the MongoDB runCommand structure
type RunCommand struct {
	Command map[string]interface{} `json:"command"`
}

// Change represents a single change in the changeset
type Change struct {
	RunCommand RunCommand `json:"runCommand"`
}

// ChangeSet represents the changeset structure
type ChangeSet struct {
	ID      string   `json:"id"`
	Author  string   `json:"author"`
	Comment string   `json:"comment"`
	Changes []Change `json:"changes"`
}

// DatabaseChangeLog represents the root structure
type DatabaseChangeLog struct {
	DatabaseChangeLog []map[string]ChangeSet `json:"databaseChangeLog"`
}

// DefaultConfig returns the default configuration
func DefaultConfig() Config {
	return Config{
		ChangesetID: "rocket-r14-02-phrase-common-payment-credit_v3",
		Author:      "rocket",
		Comment:     "[r14] Upsert Phrases For Common Payment and Credit Card",
		InputFile:   "input.txt",
		OutputFile:  "db.changeset.json",
	}
}

// NewMongoDate creates a MongoDate with current timestamp
func NewMongoDate() MongoDate {
	return MongoDate{
		Date: time.Now().UTC().Format("2006-01-02T15:04:05.000Z"),
	}
}
