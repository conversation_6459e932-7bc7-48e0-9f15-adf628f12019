package main

import (
	"strings"
)

// ModuleInfo holds the extracted module information
type ModuleInfo struct {
	ModuleKey  string
	ModuleName string
}

// ExtractModuleInfo extracts module_key and module_name from phrase_key
// Following the specific extraction rules from the PRD
func ExtractModuleInfo(phraseKey string) ModuleInfo {
	// Special case for api_error
	if strings.HasPrefix(phraseKey, "api_error_") {
		return ModuleInfo{
			ModuleKey:  "api_error",
			ModuleName: "Api_Error",
		}
	}

	// Standard modules: extract prefix before first underscore
	parts := strings.Split(phraseKey, "_")
	if len(parts) == 0 {
		return ModuleInfo{
			ModuleKey:  "",
			ModuleName: "",
		}
	}

	prefix := parts[0]
	moduleKey := strings.ToLower(prefix)

	// Capitalize first letter for module name
	var moduleName string
	if len(prefix) > 0 {
		moduleName = strings.ToUpper(string(prefix[0])) + strings.ToLower(prefix[1:])
	}

	return ModuleInfo{
		ModuleKey:  moduleKey,
		ModuleName: moduleName,
	}
}

// Examples of module extraction:
// label_enter_detail_v2_cc_full → module_key: "label", module_name: "Label"
// api_error_invalid_request → module_key: "api_error", module_name: "Api_Error"
// error_network_timeout → module_key: "error", module_name: "Error"
// button_submit_form → module_key: "button", module_name: "Button"
