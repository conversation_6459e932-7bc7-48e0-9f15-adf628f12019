package main

import (
	"strings"
)

// GenerateID generates the MongoDB document ID from phrase_key
// Following the specific capitalization rules from the PRD
func GenerateID(phraseKey string) string {
	// Special case for API error phrases
	if strings.HasPrefix(phraseKey, "api_error_") {
		// Remove "api_error_" prefix and add "Api_Error_"
		return "Api_Error_" + phraseKey[10:]
	}

	// Standard rule: capitalize first character
	if len(phraseKey) == 0 {
		return phraseKey
	}

	return strings.ToUpper(string(phraseKey[0])) + phraseKey[1:]
}

// Examples of ID generation:
// label_enter_detail_v2_cc_full → Label_enter_detail_v2_cc_full
// api_error_invalid_request → Api_Error_invalid_request
// error_network_timeout → Error_network_timeout
// button_submit_form → Button_submit_form
