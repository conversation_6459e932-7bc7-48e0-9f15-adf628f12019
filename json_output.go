package main

import (
	"encoding/json"
	"fmt"
	"os"
)

// WriteJSONToFile writes the changeset to a JSON file with proper formatting
func WriteJSONToFile(changeset DatabaseChangeLog, filename string) error {
	// Marshal with indentation for readability
	jsonData, err := json.MarshalIndent(changeset, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// Write to file
	err = os.WriteFile(filename, jsonData, 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %s: %w", filename, err)
	}

	return nil
}

// GenerateJSONString returns the changeset as a formatted JSON string
func GenerateJSONString(changeset DatabaseChangeLog) (string, error) {
	jsonData, err := json.MarshalIndent(changeset, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal JSO<PERSON>: %w", err)
	}

	return string(jsonData), nil
}

// ValidateJSON validates that the generated JSON is valid
func ValidateJSON(data []byte) error {
	var temp interface{}
	err := json.Unmarshal(data, &temp)
	if err != nil {
		return fmt.Errorf("invalid JSON: %w", err)
	}
	return nil
}
