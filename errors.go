package main

import (
	"fmt"
	"os"
)

// AppError represents application-specific errors
type AppError struct {
	Type    string
	Message string
	Err     error
}

func (e AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s: %v", e.Type, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Error types
const (
	ErrorTypeFileIO     = "FILE_IO_ERROR"
	ErrorTypeValidation = "VALIDATION_ERROR"
	ErrorTypeJSON       = "JSON_ERROR"
	ErrorTypeConfig     = "CONFIG_ERROR"
	ErrorTypeModule     = "MODULE_ERROR"
	ErrorTypeParsing    = "PARSING_ERROR"
)

// NewFileIOError creates a file I/O error
func NewFileIOError(message string, err error) AppError {
	return AppError{
		Type:    ErrorTypeFileIO,
		Message: message,
		Err:     err,
	}
}

// NewValidationError creates a validation error
func NewValidationError(message string) AppError {
	return AppError{
		Type:    ErrorTypeValidation,
		Message: message,
	}
}

// NewJSONError creates a JSON processing error
func NewJSONError(message string, err error) AppError {
	return AppError{
		Type:    ErrorTypeJSON,
		Message: message,
		Err:     err,
	}
}

// NewConfigError creates a configuration error
func NewConfigError(message string) AppError {
	return AppError{
		Type:    ErrorTypeConfig,
		Message: message,
	}
}

// NewModuleError creates a module-related error
func NewModuleError(message string) AppError {
	return AppError{
		Type:    ErrorTypeModule,
		Message: message,
	}
}

// NewParsingError creates a parsing error
func NewParsingError(message string, err error) AppError {
	return AppError{
		Type:    ErrorTypeParsing,
		Message: message,
		Err:     err,
	}
}

// HandleError handles errors gracefully and exits if necessary
func HandleError(err error) {
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

// HandleErrorWithCode handles errors with specific exit codes
func HandleErrorWithCode(err error, exitCode int) {
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(exitCode)
	}
}
