package main

import (
	"time"
)

// GenerateTimestamp creates a current timestamp in MongoDB date format
// Returns ISO8601 format with milliseconds and UTC timezone
func GenerateTimestamp() MongoDate {
	// Get current time in UTC
	now := time.Now().UTC()

	// Format to ISO8601 with milliseconds
	// MongoDB expects format like: "2025-08-02T10:30:45.123Z"
	timestamp := now.Format("2006-01-02T15:04:05.000Z")

	return MongoDate{
		Date: timestamp,
	}
}

// GenerateTimestampString returns just the timestamp string
func GenerateTimestampString() string {
	return time.Now().UTC().Format("2006-01-02T15:04:05.000Z")
}

// Example output: {"$date": "2025-08-02T10:30:45.123Z"}
